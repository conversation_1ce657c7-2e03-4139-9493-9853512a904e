import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import '../../models/order_model.dart';
import '../../models/offer_model.dart';
import '../../models/user_model.dart';
import '../../providers/language_provider.dart';
import '../../providers/demo_auth_provider.dart';
import '../../providers/theme_provider.dart';
import '../../services/order_service.dart';
import '../../services/offer_service.dart';
import '../chat/chat_screen.dart';
import 'order_detail_view.dart';

class MyOrdersPage extends StatefulWidget {
  const MyOrdersPage({super.key});

  @override
  State<MyOrdersPage> createState() => _MyOrdersPageState();
}

class _MyOrdersPageState extends State<MyOrdersPage> with TickerProviderStateMixin {
  late TabController _tabController;
  late AnimationController _animationController;
  List<OrderModel> _orders = [];
  final Map<String, List<OfferModel>> _orderOffers = {}; // Map order ID to offers
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
    _tabController.addListener(() {
      setState(() {}); // Rebuild to update tab colors
    });

    _animationController = AnimationController(duration: const Duration(seconds: 2), vsync: this)
      ..repeat(reverse: true);

    _ensureUserLoggedIn();
    _loadOrders();
  }

  void _ensureUserLoggedIn() {
    final authProvider = Provider.of<DemoAuthProvider>(context, listen: false);
    if (!authProvider.isAuthenticated) {
      authProvider.quickLogin(UserRole.client);
    }
  }

  @override
  void dispose() {
    _tabController.dispose();
    _animationController.dispose();
    super.dispose();
  }

  Future<void> _loadOrders() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final authProvider = Provider.of<DemoAuthProvider>(context, listen: false);
      final userId = authProvider.user?.id;

      if (userId != null) {
        final orders = await OrderService.getOrders(clientId: userId);
        setState(() {
          _orders = orders;
          _isLoading = false;
        });
        // Load offers after orders are loaded
        await _loadOffers();
      } else {
        setState(() {
          _orders = [];
          _isLoading = false;
        });
      }
    } catch (e) {
      setState(() {
        _orders = [];
        _isLoading = false;
      });
    }
  }

  Future<void> _loadOffers() async {
    try {
      // Load demo offers first
      OfferService.addDemoOffers();

      // Load offers for each order
      for (final order in _orders) {
        try {
          final offers = await OfferService.getOffersForRequest(order.requestId); // Use requestId, not order.id
          _orderOffers[order.id] = offers;
        } catch (e) {
          _orderOffers[order.id] = []; // Set empty list on error
        }
      }

      if (mounted) {
        setState(() {});
      }
    } catch (e) {
      // Handle error silently or log to proper logging service
    }
  }

  @override
  Widget build(BuildContext context) {
    final languageProvider = Provider.of<LanguageProvider>(context);
    final isArabic = languageProvider.isArabic;
    final isDark = Theme.of(context).brightness == Brightness.dark;

    return Directionality(
      textDirection: isArabic ? TextDirection.rtl : TextDirection.ltr,
      child: Scaffold(
        body: Column(
          children: [
            // Status Filter Bar (No Header Background)
            Padding(
              padding: const EdgeInsets.only(top: 8),
              child: Column(
                children: [
                  // Tab Bar Section
                  // Enhanced Status Filter Bar (Matching Freelancer Offers Design)
                  Container(
                    padding: const EdgeInsets.fromLTRB(16, 8, 16, 16),
                    child: Container(
                      padding: const EdgeInsets.all(4),
                      decoration: BoxDecoration(
                        color:
                            isDark
                                ? ThemeProvider.darkCardBackground.withValues(alpha: 0.8)
                                : Theme.of(context).colorScheme.surface.withValues(alpha: 0.8),
                        borderRadius: BorderRadius.circular(16),
                        border: Border.all(color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.2)),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withValues(alpha: 0.05),
                            blurRadius: 8,
                            offset: const Offset(0, 2),
                          ),
                        ],
                      ),
                      child: Row(
                        children: [
                          // Waiting Orders - انتظار
                          Expanded(
                            child: buildEnhancedStatusTab(
                              icon: Icons.hourglass_empty,
                              label: isArabic ? 'انتظار' : 'Waiting',
                              count: _getWaitingOrders().length,
                              color: Colors.blue,
                              isSelected: _tabController.index == 0,
                              onTap: () => _tabController.animateTo(0),
                            ),
                          ),
                          // In Progress - قيد التنفيذ
                          Expanded(
                            child: buildEnhancedStatusTab(
                              icon: Icons.work_outline,
                              label: isArabic ? 'قيد التنفيذ' : 'In Progress',
                              count: _getInProgressOrders().length,
                              color: Colors.purple,
                              isSelected: _tabController.index == 1,
                              onTap: () => _tabController.animateTo(1),
                            ),
                          ),
                          // Completed - مكتمل
                          Expanded(
                            child: buildEnhancedStatusTab(
                              icon: Icons.check_circle_outline,
                              label: isArabic ? 'مكتمل' : 'Completed',
                              count: _getCompletedOrders().length,
                              color: Colors.green,
                              isSelected: _tabController.index == 2,
                              onTap: () => _tabController.animateTo(2),
                            ),
                          ),
                          // Canceled - ملغي
                          Expanded(
                            child: buildEnhancedStatusTab(
                              icon: Icons.cancel_outlined,
                              label: isArabic ? 'ملغي' : 'Canceled',
                              count: _getCanceledOrders().length,
                              color: Colors.red,
                              isSelected: _tabController.index == 3,
                              onTap: () => _tabController.animateTo(3),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),

            // Content Section
            Expanded(
              child:
                  _isLoading
                      ? const Center(child: CircularProgressIndicator())
                      : GestureDetector(
                        onHorizontalDragEnd: (details) {
                          // Detect swipe direction and navigate
                          final velocity = details.primaryVelocity;
                          if (velocity != null) {
                            if (velocity > 0) {
                              // Swipe right - go to previous tab
                              if (_tabController.index > 0) {
                                _tabController.animateTo(_tabController.index - 1);
                              }
                            } else if (velocity < 0) {
                              // Swipe left - go to next tab
                              if (_tabController.index < 3) {
                                _tabController.animateTo(_tabController.index + 1);
                              }
                            }
                          }
                        },
                        child: TabBarView(
                          controller: _tabController,
                          children: [
                            _buildOrdersList(_getWaitingOrders(), isArabic, isDark, 'waiting'),
                            _buildOrdersList(_getInProgressOrders(), isArabic, isDark, 'inProgress'),
                            _buildOrdersList(_getCompletedOrders(), isArabic, isDark, 'completed'),
                            _buildOrdersList(_getCanceledOrders(), isArabic, isDark, 'canceled'),
                          ],
                        ),
                      ),
            ),
          ],
        ),
      ),
    );
  }

  // Filter orders by status
  List<OrderModel> _getWaitingOrders() {
    return _orders
        .where((order) => order.status == OrderStatus.created || order.status == OrderStatus.paymentPending)
        .toList();
  }

  List<OrderModel> _getInProgressOrders() {
    return _orders
        .where(
          (order) =>
              order.status == OrderStatus.paymentConfirmed ||
              order.status == OrderStatus.inProgress ||
              order.status == OrderStatus.submitted ||
              order.status == OrderStatus.delivered,
        )
        .toList();
  }

  List<OrderModel> _getCompletedOrders() {
    return _orders.where((order) => order.status == OrderStatus.completed).toList();
  }

  List<OrderModel> _getCanceledOrders() {
    return _orders.where((order) => order.status == OrderStatus.cancelled).toList();
  }

  Widget _buildOrdersList(List<OrderModel> orders, bool isArabic, bool isDark, String category) {
    if (orders.isEmpty) {
      return _buildEmptyState(category, isArabic, isDark);
    }

    return RefreshIndicator(
      onRefresh: _loadOrders,
      child: ListView.builder(
        key: ValueKey('orders_list_$category'),
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        itemCount: orders.length,
        itemBuilder: (context, index) {
          final order = orders[index];
          return _buildOrderCard(order, isArabic, isDark);
        },
      ),
    );
  }

  Widget _buildEmptyState(String category, bool isArabic, bool isDark) {
    IconData icon;
    String message;
    Color iconColor;

    switch (category) {
      case 'waiting':
        icon = Icons.hourglass_empty;
        message = isArabic ? 'لا توجد طلبات في الانتظار' : 'No waiting orders';
        iconColor = Colors.orange;
        break;
      case 'inProgress':
        icon = Icons.work_outline;
        message = isArabic ? 'لا توجد طلبات قيد التنفيذ' : 'No orders in progress';
        iconColor = Colors.orange;
        break;
      case 'completed':
        icon = Icons.check_circle_outline;
        message = isArabic ? 'لا توجد طلبات مكتملة' : 'No completed orders';
        iconColor = Colors.green;
        break;
      case 'canceled':
        icon = Icons.cancel_outlined;
        message = isArabic ? 'لا توجد طلبات ملغية' : 'No canceled orders';
        iconColor = Colors.red;
        break;
      default:
        icon = Icons.inbox_outlined;
        message = isArabic ? 'لا توجد طلبات' : 'No orders';
        iconColor = Colors.grey;
    }

    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(icon, size: 64, color: iconColor.withValues(alpha: 0.5)),
          const SizedBox(height: 16),
          Text(
            message,
            style: TextStyle(
              fontSize: 18,
              color: isDark ? Colors.grey[400] : Colors.grey[600],
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildOrderCard(OrderModel order, bool isArabic, bool isDark) {
    return Container(
      margin: const EdgeInsets.only(bottom: 10),
      decoration: BoxDecoration(
        color: isDark ? ThemeProvider.darkCardBackground : Colors.white,
        borderRadius: BorderRadius.circular(14),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: isDark ? 0.3 : 0.08),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
        border: Border.all(color: isDark ? Colors.grey[700]! : Colors.grey[200]!, width: 1),
      ),
      child: Padding(
        padding: const EdgeInsets.all(12),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header Row
            Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        _getOrderTitle(order, isArabic),
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: isDark ? ThemeProvider.darkTextPrimary : Colors.black87,
                        ),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                      const SizedBox(height: 4),
                      Row(
                        children: [
                          Text(
                            isArabic ? 'طلب #${_safeSubstring(order.id, 8)}' : 'Order #${_safeSubstring(order.id, 8)}',
                            style: TextStyle(
                              fontSize: 12,
                              color: isDark ? ThemeProvider.darkTextSecondary : Colors.grey[600],
                            ),
                          ),
                          // Only show price for non-waiting orders (when price is agreed upon)
                          if (!_isWaitingOrder(order)) ...[
                            const SizedBox(width: 8),
                            Container(
                              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                              decoration: BoxDecoration(
                                color: ThemeProvider.successGreen.withValues(alpha: 0.1),
                                borderRadius: BorderRadius.circular(12),
                                border: Border.all(color: ThemeProvider.successGreen.withValues(alpha: 0.3), width: 1),
                              ),
                              child: Text(
                                '${order.amount.toStringAsFixed(0)} ${isArabic ? 'ريال' : 'SAR'}',
                                style: const TextStyle(
                                  fontSize: 12,
                                  fontWeight: FontWeight.w600,
                                  color: ThemeProvider.successGreen,
                                ),
                              ),
                            ),
                          ],
                        ],
                      ),
                    ],
                  ),
                ),
                const SizedBox(width: 12),
                // Delete button for waiting orders only
                if (_isWaitingOrder(order))
                  GestureDetector(
                    onTap: () => _showDeleteOrderDialog(order, isArabic),
                    child: Container(
                      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                      decoration: BoxDecoration(
                        color: Colors.red[50],
                        borderRadius: BorderRadius.circular(6),
                        border: Border.all(color: Colors.red[200]!, width: 1),
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Icon(Icons.delete_outline, color: Colors.red[400], size: 16),
                          const SizedBox(width: 4),
                          Text(
                            isArabic ? 'حذف' : 'Delete',
                            style: TextStyle(color: Colors.red[400], fontSize: 12, fontWeight: FontWeight.w600),
                          ),
                        ],
                      ),
                    ),
                  ),
                if (_isWaitingOrder(order)) const SizedBox(width: 8),
                _buildStatusBadge(order.status, isArabic),
              ],
            ),

            // Freelancer Info Row (only show for non-waiting orders)
            if (!_isWaitingOrder(order)) ...[
              const SizedBox(height: 10),
              Row(
                children: [
                  CircleAvatar(
                    radius: 20,
                    backgroundColor: ThemeProvider.primaryBlue.withValues(alpha: 0.1),
                    child: const Icon(Icons.person, color: ThemeProvider.primaryBlue, size: 20),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          isArabic ? 'المستقل المكلف' : 'Assigned Freelancer',
                          style: TextStyle(
                            fontSize: 12,
                            color: isDark ? ThemeProvider.darkTextSecondary : Colors.grey[600],
                          ),
                        ),
                        const SizedBox(height: 2),
                        Text(
                          'أحمد محمد', // Demo freelancer name
                          style: TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.w600,
                            color: isDark ? ThemeProvider.darkTextPrimary : Colors.black87,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ],

            const SizedBox(height: 10),

            // Order Tracking Timeline
            _buildOrderTimeline(order, isArabic, isDark),

            const SizedBox(height: 10),

            // Offers Button (only for waiting orders with offers)
            if (_isWaitingOrder(order) && (_orderOffers[order.id]?.isNotEmpty ?? false)) ...[
              const SizedBox(height: 10),
              _buildAnimatedOffersButton(order, isArabic),
            ],

            // Action Buttons Row
            Row(
              children: [
                // Smaller Details button to focus attention on offers
                Expanded(
                  child: OutlinedButton.icon(
                    onPressed: () => _openOrderDetails(order, isArabic),
                    icon: const Icon(Icons.visibility_outlined, size: 14),
                    label: Text(isArabic ? 'التفاصيل' : 'Details', style: const TextStyle(fontSize: 11)),
                    style: OutlinedButton.styleFrom(
                      foregroundColor: Colors.grey[600],
                      side: BorderSide(color: Colors.grey[300]!, width: 1),
                      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
                      minimumSize: const Size(80, 32),
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }



  // Helper Methods
  Widget _buildStatusBadge(OrderStatus status, bool isArabic) {
    Color color;
    String text;
    IconData icon;

    switch (status) {
      case OrderStatus.created:
      case OrderStatus.paymentPending:
        color = Colors.orange;
        text = isArabic ? 'في الانتظار' : 'Waiting';
        icon = Icons.hourglass_empty;
        break;
      case OrderStatus.paymentConfirmed:
      case OrderStatus.inProgress:
        color = Colors.blue;
        text = isArabic ? 'قيد التنفيذ' : 'In Progress';
        icon = Icons.work_outline;
        break;
      case OrderStatus.submitted:
      case OrderStatus.delivered:
        color = Colors.teal;
        text = isArabic ? 'مُسلَّم' : 'Delivered';
        icon = Icons.check_circle_outline;
        break;
      case OrderStatus.completed:
        color = Colors.green;
        text = isArabic ? 'مكتمل' : 'Completed';
        icon = Icons.verified;
        break;
      case OrderStatus.cancelled:
        color = Colors.red;
        text = isArabic ? 'ملغي' : 'Canceled';
        icon = Icons.cancel_outlined;
        break;
      default:
        color = Colors.grey;
        text = isArabic ? 'غير محدد' : 'Unknown';
        icon = Icons.help_outline;
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, size: 12, color: color),
          const SizedBox(width: 4),
          Text(text, style: TextStyle(color: color, fontWeight: FontWeight.bold, fontSize: 10)),
        ],
      ),
    );
  }

  String _getOrderTitle(OrderModel order, bool isArabic) {
    // Map order IDs to service titles for demo
    final serviceTitles = {
      'order_001': isArabic ? 'تصميم شعار احترافي للشركة' : 'Professional Company Logo Design',
      'order_002': isArabic ? 'تطوير تطبيق جوال بـ Flutter' : 'Flutter Mobile App Development',
      'order_003': isArabic ? 'ترجمة مقال علمي من الإنجليزية للعربية' : 'Scientific Article Translation EN-AR',
      'order_004': isArabic ? 'تحليل البيانات باستخدام SPSS' : 'Data Analysis using SPSS',
      'order_005': isArabic ? 'مراجعة وتدقيق نص أكاديمي' : 'Academic Text Review & Proofreading',
    };

    return serviceTitles[order.id] ?? (isArabic ? 'خدمة عامة' : 'General Service');
  }

  String _safeSubstring(String text, int maxLength) {
    if (text.length <= maxLength) {
      return text;
    }
    return text.substring(0, maxLength);
  }

  // Order Timeline Widget
  Widget _buildOrderTimeline(OrderModel order, bool isArabic, bool isDark) {
    final steps = getTimelineSteps(order, isArabic);
    final currentStep = getCurrentStepIndex(order.status);

    return Padding(
      padding: const EdgeInsets.all(8),
      child: Row(
        children:
            steps.asMap().entries.map((entry) {
                final index = entry.key;
                final step = entry.value;
                final isActive = index <= currentStep;
                final isLast = index == steps.length - 1;

                return Expanded(
                  child: Row(
                    children: [
                      Expanded(
                        child: Column(
                          children: [
                            Container(
                              width: 24,
                              height: 24,
                              decoration: BoxDecoration(
                                color:
                                    isActive ? ThemeProvider.primaryBlue : (isDark ? Colors.grey[600] : Colors.grey[300]),
                                shape: BoxShape.circle,
                              ),
                              child: Icon(isActive ? Icons.check : Icons.circle, size: 12, color: Colors.white),
                            ),
                            const SizedBox(height: 4),
                            Text(
                              step,
                              style: TextStyle(
                                fontSize: 10,
                                fontWeight: isActive ? FontWeight.bold : FontWeight.normal,
                                color:
                                    isActive
                                        ? ThemeProvider.primaryBlue
                                        : (isDark ? ThemeProvider.darkTextSecondary : Colors.grey[600]),
                              ),
                              textAlign: TextAlign.center,
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                            ),
                          ],
                        ),
                      ),
                      if (!isLast)
                        Container(
                          height: 2,
                          width: 20,
                          color: isActive ? ThemeProvider.primaryBlue : (isDark ? Colors.grey[600] : Colors.grey[300]),
                        ),
                    ],
                  ),
                );
              }).toList(),
        ),
      ),
    )

  // Timeline Helper Methods
  List<String> getTimelineSteps(OrderModel order, bool isArabic) {
    if (isArabic) {
      return ['إنشاء', 'دفع', 'تنفيذ', 'مكتمل'];
    } else {
      return ['Created', 'Payment', 'Progress', 'Complete'];
    }
  }

  int getCurrentStepIndex(OrderStatus status) {
    switch (status) {
      case OrderStatus.created:
        return 0;
      case OrderStatus.paymentPending:
        return 0;
      case OrderStatus.paymentConfirmed:
      case OrderStatus.inProgress:
        return 2;
      case OrderStatus.submitted:
      case OrderStatus.delivered:
      case OrderStatus.editing:
      case OrderStatus.completed:
        return 3;
      case OrderStatus.cancelled:
        return 0;
    }
  }





  void openOrderDetails(OrderModel order, bool isArabic) {
    Navigator.push(context, MaterialPageRoute(builder: (context) => OrderDetailView(order: order, isArabic: isArabic)));
  }

  Widget buildAnimatedOffersButton(OrderModel order, bool isArabic) {
    final offers = _orderOffers[order.id] ?? [];
    final hasNewOffers = hasNewOffers0(order.id);

    return AnimatedContainer(
      duration: const Duration(milliseconds: 300),
      width: double.infinity,
      child: Stack(
        children: [
          // Pulse animation for new offers
          if (hasNewOffers)
            Positioned.fill(
              child: AnimatedBuilder(
                animation: _animationController,
                builder: (context, child) {
                  return Container(
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(10),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.orange.withValues(alpha: 0.4 * _animationController.value),
                          blurRadius: 20 * _animationController.value,
                          spreadRadius: 5 * _animationController.value,
                        ),
                      ],
                    ),
                  );
                },
              ),
            ),
          // Main button
          ElevatedButton.icon(
            onPressed: () {
              HapticFeedback.lightImpact();
              showOffersModal(order);
            },
            icon: const Icon(Icons.local_offer, size: 18),
            label: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Expanded(
                  child: Text(
                    isArabic ? 'عرض العروض المستلمة (${offers.length})' : 'View Received Offers (${offers.length})',
                    style: const TextStyle(fontSize: 14, fontWeight: FontWeight.w600),
                  ),
                ),
                if (hasNewOffers) ...[
                  const SizedBox(width: 8),
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                    decoration: BoxDecoration(color: Colors.red, borderRadius: BorderRadius.circular(10)),
                    child: Text(
                      isArabic ? 'جديد' : 'New',
                      style: const TextStyle(color: Colors.white, fontSize: 10, fontWeight: FontWeight.bold),
                    ),
                  ),
                ],
              ],
            ),
            style: ElevatedButton.styleFrom(
              backgroundColor: hasNewOffers ? Colors.deepOrange : Colors.orange,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(vertical: 12),
              shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
              elevation: hasNewOffers ? 4 : 2,
            ),
          ),
        ],
      ),
    );
  }

  bool hasNewOffers0(String orderId) {
    final offers = _orderOffers[orderId] ?? [];
    return offers.any((offer) => DateTime.now().difference(offer.createdAt).inHours < 2);
  }

  void showOffersModal(OrderModel order) {
    final languageProvider = Provider.of<LanguageProvider>(context, listen: false);
    final isArabic = languageProvider.isArabic;
    final offers = _orderOffers[order.id] ?? [];

    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder:
          (context) => _EnhancedOffersModal(
            offers: offers,
            isArabic: isArabic,
            onOfferAction: (offer, action) {
              Navigator.pop(context);
              if (action == 'accept') {
                acceptOffer(offer);
              } else if (action == 'reject') {
                rejectOffer(offer);
              }
            },
          ),
    );
  }

  Widget buildEnhancedStatusTab({
    required IconData icon,
    required String label,
    required int count,
    required Color color,
    required bool isSelected,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 200),
        curve: Curves.easeInOut,
        margin: const EdgeInsets.symmetric(horizontal: 2),
        padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 8),
        decoration: BoxDecoration(
          gradient:
              isSelected
                  ? LinearGradient(
                    colors: [color, color.withValues(alpha: 0.8)],
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                  )
                  : null,
          color: isSelected ? null : Colors.transparent,
          borderRadius: BorderRadius.circular(12),
          boxShadow:
              isSelected
                  ? [BoxShadow(color: color.withValues(alpha: 0.3), blurRadius: 8, offset: const Offset(0, 2))]
                  : null,
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(icon, size: 20, color: isSelected ? Colors.white : color.withValues(alpha: 0.7)),
            const SizedBox(height: 4),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
              decoration: BoxDecoration(
                color: isSelected ? Colors.white.withValues(alpha: 0.2) : color.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(10),
              ),
              child: Text(
                count.toString(),
                style: TextStyle(fontSize: 12, fontWeight: FontWeight.bold, color: isSelected ? Colors.white : color),
              ),
            ),
            const SizedBox(height: 2),
            Text(
              label,
              style: TextStyle(
                fontSize: 11,
                fontWeight: isSelected ? FontWeight.bold : FontWeight.w600,
                color: isSelected ? Colors.white : color.withValues(alpha: 0.9),
                letterSpacing: 0.3,
              ),
              textAlign: TextAlign.center,
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
          ],
        ),
      ),
    );
  }

  // Check if order is in waiting status (can be deleted)
  bool isWaitingOrder(OrderModel order) {
    return order.status == OrderStatus.created || order.status == OrderStatus.paymentPending;
  }

  // Show delete confirmation dialog
  void showDeleteOrderDialog(OrderModel order, bool isArabic) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text(isArabic ? 'حذف الطلب' : 'Delete Order'),
          content: Text(
            isArabic
                ? 'هل أنت متأكد من أنك تريد حذف هذا الطلب؟ لا يمكن التراجع عن هذا الإجراء.'
                : 'Are you sure you want to delete this order? This action cannot be undone.',
          ),
          actions: [
            TextButton(onPressed: () => Navigator.of(context).pop(), child: Text(isArabic ? 'إلغاء' : 'Cancel')),
            ElevatedButton(
              onPressed: () {
                Navigator.of(context).pop();
                deleteOrder(order, isArabic);
              },
              style: ElevatedButton.styleFrom(backgroundColor: Colors.red, foregroundColor: Colors.white),
              child: Text(isArabic ? 'حذف' : 'Delete'),
            ),
          ],
        );
      },
    );
  }

  // Delete the order
  void deleteOrder(OrderModel order, bool isArabic) {
    setState(() {
      _orders.removeWhere((o) => o.id == order.id);
    });

    // Show success message
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(isArabic ? 'تم حذف الطلب بنجاح' : 'Order deleted successfully'),
          backgroundColor: Colors.green,
          duration: const Duration(seconds: 3),
        ),
      );
    }

    // In a real app, you would also call an API to delete the order from the server
    // await OrderService.deleteOrder(order.id);
  }



  Future<void> acceptOffer(OfferModel offer) async {
    final languageProvider = Provider.of<LanguageProvider>(context, listen: false);
    final isArabic = languageProvider.isArabic;

    // Show confirmation dialog
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text(isArabic ? 'قبول العرض' : 'Accept Offer'),
          content: Text(
            isArabic
                ? 'هل أنت متأكد من قبول هذا العرض؟ سيتم رفض جميع العروض الأخرى تلقائياً.'
                : 'Are you sure you want to accept this offer? All other offers will be automatically rejected.',
          ),
          actions: [
            TextButton(onPressed: () => Navigator.of(context).pop(false), child: Text(isArabic ? 'إلغاء' : 'Cancel')),
            ElevatedButton(
              onPressed: () => Navigator.of(context).pop(true),
              style: ElevatedButton.styleFrom(
                backgroundColor: ThemeProvider.successGreen,
                foregroundColor: Colors.white,
              ),
              child: Text(isArabic ? 'قبول' : 'Accept'),
            ),
          ],
        );
      },
    );

    if (confirmed == true) {
      try {
        await OfferService.acceptOffer(offer.id);

        // Reload offers to reflect changes
        await _loadOffers();

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                isArabic ? '🎉 تم قبول العرض! في انتظار دفع العميل.' : '🎉 Offer accepted! Awaiting client payment.',
              ),
              backgroundColor: ThemeProvider.successGreen,
              duration: const Duration(seconds: 3),
            ),
          );
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(isArabic ? 'حدث خطأ أثناء قبول العرض' : 'Error accepting offer'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    }
  }

  Future<void> rejectOffer(OfferModel offer) async {
    final languageProvider = Provider.of<LanguageProvider>(context, listen: false);
    final isArabic = languageProvider.isArabic;

    // Show rejection dialog with reason
    final result = await showDialog<String>(
      context: context,
      builder: (BuildContext context) {
        final reasonController = TextEditingController();
        return AlertDialog(
          title: Text(isArabic ? 'رفض العرض' : 'Reject Offer'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(isArabic ? 'هل أنت متأكد من رفض هذا العرض؟' : 'Are you sure you want to reject this offer?'),
              const SizedBox(height: 16),
              TextField(
                controller: reasonController,
                decoration: InputDecoration(
                  labelText: isArabic ? 'سبب الرفض (اختياري)' : 'Rejection reason (optional)',
                  border: const OutlineInputBorder(),
                ),
                maxLines: 2,
              ),
            ],
          ),
          actions: [
            TextButton(onPressed: () => Navigator.of(context).pop(), child: Text(isArabic ? 'إلغاء' : 'Cancel')),
            ElevatedButton(
              onPressed: () => Navigator.of(context).pop(reasonController.text.trim()),
              style: ElevatedButton.styleFrom(backgroundColor: Colors.red, foregroundColor: Colors.white),
              child: Text(isArabic ? 'رفض' : 'Reject'),
            ),
          ],
        );
      },
    );

    if (result != null) {
      try {
        await OfferService.rejectOffer(offer.id, reason: result.isNotEmpty ? result : null);

        // Reload offers to reflect changes
        await _loadOffers();

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(isArabic ? 'تم رفض العرض' : 'Offer rejected'),
              backgroundColor: Colors.orange,
              duration: const Duration(seconds: 3),
            ),
          );
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(isArabic ? 'حدث خطأ أثناء رفض العرض' : 'Error rejecting offer'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    }
  }
}

// Enhanced Offers Modal with sorting and recommendations
class _EnhancedOffersModal extends void StatefulWidget {
  final List<OfferModel> offers;
  final bool isArabic;
  final Function(OfferModel, String) onOfferAction;

  const _EnhancedOffersModal({required this.offers, required this.isArabic, required this.onOfferAction});

  @override
  State<_EnhancedOffersModal> createState() => _EnhancedOffersModalState();
}

class _EnhancedOffersModalState extends void State<_EnhancedOffersModal> {
  String sortBy = 'price'; // price, delivery, rating
  List<OfferModel> sortedOffers = [];

  @override
  void initState() {
    super.initState();
    sortedOffers = List.from(widget.offers);
    sortOffers();
  }

  void sortOffers() {
    switch (sortBy) {
      case 'price':
        sortedOffers.sort((a, b) => a.price.compareTo(b.price));
        break;
      case 'delivery':
        sortedOffers.sort((a, b) => a.deliveryDays.compareTo(b.deliveryDays));
        break;
      case 'rating':
        sortedOffers.sort((a, b) => (b.freelancerRating ?? 0).compareTo(a.freelancerRating ?? 0));
        break;
    }
    setState(() {});
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      height: MediaQuery.of(context).size.height * 0.85,
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      child: Column(
        children: [
          // Header with sorting
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Theme.of(context).primaryColor,
              borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
            ),
            child: Column(
              children: [
                Row(
                  children: [
                    Expanded(
                      child: Text(
                        widget.isArabic ? 'العروض المستلمة' : 'Received Offers',
                        style: const TextStyle(color: Colors.white, fontSize: 18, fontWeight: FontWeight.bold),
                      ),
                    ),
                    Text(
                      '${widget.offers.length}',
                      style: const TextStyle(color: Colors.white, fontSize: 16, fontWeight: FontWeight.bold),
                    ),
                  ],
                ),
                const SizedBox(height: 12),
                // Sorting options
                Row(
                  children: [
                    Text(
                      widget.isArabic ? 'ترتيب حسب:' : 'Sort by:',
                      style: const TextStyle(color: Colors.white70, fontSize: 14),
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: SingleChildScrollView(
                        scrollDirection: Axis.horizontal,
                        child: Row(
                          children: [
                            buildSortChip('price', widget.isArabic ? 'السعر' : 'Price'),
                            const SizedBox(width: 8),
                            buildSortChip('delivery', widget.isArabic ? 'التسليم' : 'Delivery'),
                            const SizedBox(width: 8),
                            buildSortChip('rating', widget.isArabic ? 'التقييم' : 'Rating'),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
          // Offers List
          Expanded(
            child:
                sortedOffers.isEmpty
                    ? Center(
                      child: Text(
                        widget.isArabic ? 'لا توجد عروض بعد' : 'No offers yet',
                        style: TextStyle(color: Colors.grey[600], fontSize: 16),
                      ),
                    )
                    : ListView.builder(
                      padding: const EdgeInsets.all(16),
                      itemCount: sortedOffers.length,
                      itemBuilder: (context, index) {
                        final offer = sortedOffers[index];
                        final isDark = Theme.of(context).brightness == Brightness.dark;
                        return buildEnhancedOfferCard(offer, widget.isArabic, isDark, false);
                      },
                    ),
          ),
        ],
      ),
    );
  }

  Widget buildSortChip(String value, String label) {
    final isSelected = sortBy == value;
    return GestureDetector(
      onTap: () {
        HapticFeedback.selectionClick();
        setState(() {
          sortBy = value;
          sortOffers();
        });
      },
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
        decoration: BoxDecoration(
          color: isSelected ? Colors.white : Colors.white.withValues(alpha: 0.2),
          borderRadius: BorderRadius.circular(16),
        ),
        child: Text(
          label,
          style: TextStyle(
            color: isSelected ? Theme.of(context).primaryColor : Colors.white,
            fontSize: 12,
            fontWeight: FontWeight.w600,
          ),
        ),
      ),
    );
  }

  Widget buildEnhancedOfferCard(OfferModel offer, bool isArabic, bool isDark, bool isRecommended) {
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: isDark ? Colors.grey[800] : Colors.grey[50],
        borderRadius: BorderRadius.circular(10),
        border: Border.all(color: isDark ? Colors.grey[600]! : Colors.grey[300]!, width: 1),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Freelancer Info Row
          Row(
            children: [
              CircleAvatar(
                radius: 16,
                backgroundColor: ThemeProvider.primaryBlue.withValues(alpha: 0.1),
                child: Text(
                  offer.freelancerAvatar.isNotEmpty ? offer.freelancerAvatar : '👨‍💻',
                  style: const TextStyle(fontSize: 16),
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      offer.freelancerName,
                      style: TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.bold,
                        color: isDark ? ThemeProvider.darkTextPrimary : Colors.black87,
                      ),
                    ),
                    const SizedBox(height: 2),
                    Row(
                      children: [
                        Icon(Icons.star, size: 14, color: Colors.amber[600]),
                        const SizedBox(width: 2),
                        Text(
                          offer.freelancerRating?.toStringAsFixed(1) ?? 'N/A',
                          style: TextStyle(
                            fontSize: 11,
                            color: isDark ? ThemeProvider.darkTextSecondary : Colors.grey[600],
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
              Column(
                crossAxisAlignment: CrossAxisAlignment.end,
                children: [
                  Text(
                    '${offer.price.toStringAsFixed(0)} ${isArabic ? 'ريال' : 'SAR'}',
                    style: const TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.bold,
                      color: ThemeProvider.successGreen,
                    ),
                  ),
                  Text(
                    '${offer.deliveryDays} ${isArabic ? 'أيام' : 'days'}',
                    style: TextStyle(fontSize: 11, color: isDark ? ThemeProvider.darkTextSecondary : Colors.grey[600]),
                  ),
                ],
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            offer.description,
            style: TextStyle(fontSize: 12, color: isDark ? ThemeProvider.darkTextSecondary : Colors.grey[700]),
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
          const SizedBox(height: 8),
          Row(
            children: [
              Expanded(
                child: OutlinedButton.icon(
                  onPressed: () => startChatWithFreelancer(offer),
                  icon: const Icon(Icons.chat_bubble_outline, size: 16),
                  label: Text(isArabic ? 'بدء محادثة' : 'Start Chat', style: const TextStyle(fontSize: 12)),
                  style: OutlinedButton.styleFrom(
                    padding: const EdgeInsets.symmetric(vertical: 8),
                    shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
                  ),
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: ElevatedButton.icon(
                  onPressed: () {
                    HapticFeedback.mediumImpact();
                    widget.onOfferAction(offer, 'accept');
                  },
                  icon: const Icon(Icons.check, size: 16),
                  label: Text(isArabic ? 'قبول العرض' : 'Accept Offer', style: const TextStyle(fontSize: 12)),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: ThemeProvider.successGreen,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 12),
                    shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
                  ),
                ),
              ),
              const SizedBox(width: 8),
              OutlinedButton.icon(
                onPressed: () => widget.onOfferAction(offer, 'reject'),
                icon: const Icon(Icons.close, size: 16),
                label: Text(isArabic ? 'رفض' : 'Reject', style: const TextStyle(fontSize: 12)),
                style: OutlinedButton.styleFrom(
                  foregroundColor: Colors.red,
                  padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 12),
                  shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  void startChatWithFreelancer(OfferModel offer) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder:
            (context) => ChatScreen(
              chatId: 'offer_${offer.id}',
              recipientName: offer.freelancerName,
              requestTitle: widget.isArabic ? 'عرض من ${offer.freelancerName}' : 'Offer from ${offer.freelancerName}',
              isAdminChat: false,
              orderId: offer.id,
            ),
      ),
    );
  }
}
