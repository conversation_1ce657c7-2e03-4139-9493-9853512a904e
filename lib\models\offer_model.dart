enum OfferStatus { pending, accepted, rejected, withdrawn, completed }

enum DeliveryTimeUnit { hours, days, weeks }

class OfferModel {
  final String id;
  final String requestId;
  final String freelancerId;
  final String freelancerName;
  final String freelancerAvatar;
  final double? freelancerRating;
  final double price;
  final String description;
  final int deliveryDays;
  final DeliveryTimeUnit deliveryTimeUnit;
  final OfferStatus status;
  final DateTime createdAt;
  final DateTime? updatedAt;
  final DateTime? acceptedAt;
  final DateTime? rejectedAt;
  final DateTime? withdrawnAt;
  final String? rejectionReason;
  final DateTime? submissionDate;
  final double? clientRating;
  final String? clientReview;

  OfferModel({
    required this.id,
    required this.requestId,
    required this.freelancerId,
    required this.freelancerName,
    required this.freelancerAvatar,
    this.freelancerRating,
    required this.price,
    required this.description,
    required this.deliveryDays,
    this.deliveryTimeUnit = DeliveryTimeUnit.days,
    this.status = OfferStatus.pending,
    required this.createdAt,
    this.updatedAt,
    this.acceptedAt,
    this.rejectedAt,
    this.withdrawnAt,
    this.rejectionReason,
    this.submissionDate,
    this.clientRating,
    this.clientReview,
  });

  factory OfferModel.fromJson(Map<String, dynamic> json) {
    return OfferModel(
      id: json['id'],
      requestId: json['request_id'],
      freelancerId: json['freelancer_id'],
      freelancerName: json['freelancer_name'] ?? '',
      freelancerAvatar: json['freelancer_avatar'] ?? '',
      freelancerRating: json['freelancer_rating']?.toDouble() ?? 0.0,
      price: json['price'].toDouble(),
      description: json['description'],
      deliveryDays: json['delivery_days'],
      deliveryTimeUnit:
          json['delivery_time_unit'] != null
              ? DeliveryTimeUnit.values.firstWhere((e) => e.toString().split('.').last == json['delivery_time_unit'])
              : DeliveryTimeUnit.days,
      status: OfferStatus.values.firstWhere((e) => e.toString().split('.').last == json['status']),
      createdAt: DateTime.parse(json['created_at']),
      updatedAt: json['updated_at'] != null ? DateTime.parse(json['updated_at']) : null,
      acceptedAt: json['accepted_at'] != null ? DateTime.parse(json['accepted_at']) : null,
      rejectedAt: json['rejected_at'] != null ? DateTime.parse(json['rejected_at']) : null,
      withdrawnAt: json['withdrawn_at'] != null ? DateTime.parse(json['withdrawn_at']) : null,
      rejectionReason: json['rejection_reason'],
      submissionDate: json['submission_date'] != null ? DateTime.parse(json['submission_date']) : null,
      clientRating: json['client_rating']?.toDouble(),
      clientReview: json['client_review'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'request_id': requestId,
      'freelancer_id': freelancerId,
      'freelancer_name': freelancerName,
      'freelancer_avatar': freelancerAvatar,
      'freelancer_rating': freelancerRating,
      'price': price,
      'description': description,
      'delivery_days': deliveryDays,
      'delivery_time_unit': deliveryTimeUnit.toString().split('.').last,
      'status': status.toString().split('.').last,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt?.toIso8601String(),
      'accepted_at': acceptedAt?.toIso8601String(),
      'rejected_at': rejectedAt?.toIso8601String(),
      'withdrawn_at': withdrawnAt?.toIso8601String(),
      'rejection_reason': rejectionReason,
      'submission_date': submissionDate?.toIso8601String(),
      'client_rating': clientRating,
      'client_review': clientReview,
    };
  }

  OfferModel copyWith({
    String? id,
    String? requestId,
    String? freelancerId,
    String? freelancerName,
    String? freelancerAvatar,
    double? freelancerRating,
    double? price,
    String? description,
    int? deliveryDays,
    DeliveryTimeUnit? deliveryTimeUnit,
    OfferStatus? status,
    DateTime? createdAt,
    DateTime? updatedAt,
    DateTime? acceptedAt,
    DateTime? rejectedAt,
    DateTime? withdrawnAt,
    String? rejectionReason,
    DateTime? submissionDate,
    double? clientRating,
    String? clientReview,
  }) {
    return OfferModel(
      id: id ?? this.id,
      requestId: requestId ?? this.requestId,
      freelancerId: freelancerId ?? this.freelancerId,
      freelancerName: freelancerName ?? this.freelancerName,
      freelancerAvatar: freelancerAvatar ?? this.freelancerAvatar,
      freelancerRating: freelancerRating ?? this.freelancerRating,
      price: price ?? this.price,
      description: description ?? this.description,
      deliveryDays: deliveryDays ?? this.deliveryDays,
      deliveryTimeUnit: deliveryTimeUnit ?? this.deliveryTimeUnit,
      status: status ?? this.status,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      acceptedAt: acceptedAt ?? this.acceptedAt,
      rejectedAt: rejectedAt ?? this.rejectedAt,
      withdrawnAt: withdrawnAt ?? this.withdrawnAt,
      rejectionReason: rejectionReason ?? this.rejectionReason,
      submissionDate: submissionDate ?? this.submissionDate,
      clientRating: clientRating ?? this.clientRating,
      clientReview: clientReview ?? this.clientReview,
    );
  }

  // Helper getters
  bool get isPending => status == OfferStatus.pending;
  bool get isAccepted => status == OfferStatus.accepted;
  bool get isRejected => status == OfferStatus.rejected;
  bool get isWithdrawn => status == OfferStatus.withdrawn;
  bool get isCompleted => status == OfferStatus.completed;
  bool get canWithdraw => status == OfferStatus.pending;

  String get statusText => switch (status) {
    OfferStatus.pending => 'Pending',
    OfferStatus.accepted => 'Accepted',
    OfferStatus.rejected => 'Rejected',
    OfferStatus.withdrawn => 'Withdrawn',
    OfferStatus.completed => 'Completed',
  };

  String get statusTextArabic => switch (status) {
    OfferStatus.pending => 'في الانتظار',
    OfferStatus.accepted => 'مقبول',
    OfferStatus.rejected => 'مرفوض',
    OfferStatus.withdrawn => 'مسحوب',
    OfferStatus.completed => 'مكتمل',
  };

  String get deliveryTimeText => '$deliveryDays ${deliveryTimeUnit.name}';

  String get deliveryTimeTextArabic => switch (deliveryTimeUnit) {
    DeliveryTimeUnit.hours => '$deliveryDays ساعة',
    DeliveryTimeUnit.days => '$deliveryDays يوم',
    DeliveryTimeUnit.weeks => '$deliveryDays أسبوع',
  };

  String get formattedPrice => '${price.toStringAsFixed(0)} SAR';
  String get formattedPriceArabic => '${price.toStringAsFixed(0)} ريال';
}
